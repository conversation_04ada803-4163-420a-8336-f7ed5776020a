<?php

include_once 'column_validator_helper.php';

$columnValueTests = [
    # 0
    "SKID" => [
        'pattern' => '/^\d{5}_\d{3}$/',
        'canBeEmpty' => false,
    ],
    # 1
    "ADDRID" => [
        'pattern' => '/^0$/',
        'canBeEmpty' => false,
    ],
    # 2
    "STADDR" => [
        'pattern' => '/^\d{5}:::[A-Za-z0-9]+$/',
        'canBeEmpty' => false,
    ],
    # 3
    "HHLDID" => [
        'pattern' => '/^0$/',
        'canBeEmpty' => false,
    ],
    # 4
    "STHHLD" => [
        'pattern' => '/^\d{5}:[A-Za-z]+::[A-Za-z0-9]+$/',
        'canBeEmpty' => false,
    ],
    # 5
    "INDIVID" => [
        'pattern' => '/^0$/',
        'canBeEmpty' => false,
    ],
    # 6
    "ST10" => [
        'pattern' => '/^\d{5}:[A-Za-z]+:[A-Za-z]+:[A-Za-z0-9]+$/',
        'canBeEmpty' => false,
    ],
    # 7
    "FirstName" => [
        'pattern' => '/^[A-Za-z\s\'-]+$/',
        'canBeEmpty' => false,
    ],
    # 8
    "MiddleName" => [
        'pattern' => '/^[A-Z]$/',
        'canBeEmpty' => true,
    ],
    # 9
    "LastName" => [
        'pattern' => '/^[A-Za-z\s\'-]+$/',
        'canBeEmpty' => false,
    ],
    # 10
    "Address" => [
        'pattern' => '/^[A-Za-z0-9\s\'\/#-]+$/',
        'canBeEmpty' => true,
    ],
    # 11
    "Unit" => [
        'pattern' => '/^[A-Za-z0-9\s\'\/#-]+$/',
        'canBeEmpty' => true,
    ],
    # 12
    "City" => [
        'pattern' => '/^[A-Za-z\s\'-]+$/',
        'canBeEmpty' => true,
    ],
    # 13
    "State" => [
        'pattern' => '/^[A-Z]{2}$/',
        'canBeEmpty' => true,
    ],
    # 14
    "Zip" => [
        'pattern' => '/^\d{4,8}$/',
        'canBeEmpty' => true,
    ],
    # 15
    "Zip4" => [
        'pattern' => '/^\d{4}$/',
        'canBeEmpty' => true,
    ],
    # 16
    "AddrTypeIndicator" => [ //no values for this column
        'pattern' => null,
        'canBeEmpty' => true,
    ],
    # 17
    "CensusMedianHomeValue" => [
        'pattern' => '/^\d{1,5}$/',
        'canBeEmpty' => false,
    ],
    # 18
    "CensusMedianHouseholdIncome" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => false,
    ],
    # 19
    "NumSrc" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
    ],
    # 20
    "FileDate" => [
        'pattern' => '/^\d{8}$/',
        'canBeEmpty' => false,
    ],
    # 21
    "BaseRecVerificationDate" => [ //no values for this column
        'pattern' => null,
        'canBeEmpty' => true,
    ],
    # 22
    "DOB" => [
        'pattern' => '/^\d{8}$/',
        'canBeEmpty' => true,
    ],
    # 23
    "Phone" => [
        'pattern' => '/^\d{8,10}$/',
        'canBeEmpty' => true,
    ],
    # 24
    "GenderCode" => [
        'pattern' => '/^[MF]$/',
        'canBeEmpty' => true,
    ],
    # 25
    "InferredHouseholdRank" => [
        'pattern' => '/^[1-9]$/',
        'canBeEmpty' => true,
    ],
    # 26
    "EstimatedHouseholdIncome" => [
        'pattern' => '/^[A-S]$/',
        'canBeEmpty' => true,
    ],
    # 27
    "NetWorth" => [
        'pattern' => '/^[A-I]$/',
        'canBeEmpty' => true,
    ],
    # 28
    "NumberCreditLines" => [
        'pattern' => '/^[1-9]$/',
        'canBeEmpty' => true,
    ],
    # 29
    "RangeOfNewCredit" => [
        'pattern' => '/^[1-7]$/',
        'canBeEmpty' => true,
    ],
    # 30
    "Education" => [
        'pattern' => '/^[A-D]$/',
        'canBeEmpty' => true,
    ],
    # 31
    "Occupation" => [
        'pattern' => '/^[A-Z]$/',
        'canBeEmpty' => false,
    ],
    # 32
    "OccupationDetail" => [
        'pattern' => '/^[A-Z0-9]{4}$/',
        'canBeEmpty' => false,
    ],
    # 33
    "BusinessOwner" => [
        'pattern' => '/^[1-9,A]$/',
        'canBeEmpty' => true,
    ],
    # 34
    "NumberOfChildren" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => true,
    ],
    # 35
    "PresenceOfChildren" => [
        'pattern' => '/^[Y,N]$/',
        'canBeEmpty' => true,
    ],
    # 36
    "MaritalStatusInHousehold" => [
        'pattern' => '/^[SMAB]$/',
        'canBeEmpty' => true,
    ],
    # 37
    "HomeOwnerRenter" => [
        'pattern' => '/^[H,R,9]$/',
        'canBeEmpty' => true,
    ],
    # 38
    "LengthOfResidence" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
    ],
    # 39
    "DwellingType" => [
        'pattern' => '/^[S,M]$/',
        'canBeEmpty' => true,
    ],
    # 40
    "NumberOfAdults" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 41
    "HouseholdSize" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 42
    "HomeMarketValue" => [
        'pattern' => '/^\d{1,7}$/',
        'canBeEmpty' => false,
    ],
    # 43
    "GenerationsInHousehold" => [
        'pattern' => '/^[1-4]$/',
        'canBeEmpty' => true,
    ],
    # 44
    "MailOrderBuyer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 45
    "MailOrderResponder" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 46
    "OnlinePurchasingIndicator" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 47
    "MembershipClubs" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 48
    "ValuePriceGeneralMerchandiseBuyers" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 49
    "ApparelWomens" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 50
    "ApparelWomensPetite" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 51
    "ApparelWomensPlusSize" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 52
    "ApparelWomensYoung" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 53
    "ApparelMens" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 54
    "ApparelMensBigAndTall" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 55
    "ApparelMensYoung" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 56
    "ApparelChildrens" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 57
    "HealthAndBeauty" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 58
    "BeautyCosmetics" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 59
    "Jewelry" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 60
    "Luggage" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 61
    "CardHolderAmericanExpressGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 62
    "CardHolderAmericanExpressRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 63
    "CardHolderDiscoverGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 64
    "CardHolderDiscoverRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 65
    "CardHolderGasolineRetailGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 66
    "CardHolderGasolineRetailRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 67
    "CardHolderMastercardGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 68
    "CardHolderMastercardRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 69
    "CardHolderVisaGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 70
    "CardHolderVisaRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 71
    "CardHolderBank" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 72
    "CardHolderGasDeptRetail" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 73
    "CardHolderTravelEntertainment" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 74
    "CardHolderUnknownType" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 75
    "CardHolderPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 76
    "CardHolderUpscale" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 77
    "CreditCardUser" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 78
    "CreditCardNewIssue" => [
        'pattern' => '/^[B]$/', // TODO: double check this value.
        'canBeEmpty' => true,
    ],
    # 79
    "BankCardPresentInHousehold" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 80
    "InvestingActive" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 81
    "InvestingPersonal" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 82
    "InvestingRealEstate" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 83
    "InvestingStocksBonds" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 84
    "InvestingReadingNewsletterOrSubscriber" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 85
    "InvestingMoneySeeker" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 86
    "InvestingFinanceGroup" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 87
    "InvestingForeign" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 88
    "InvestingEstimatedResidentialPropertiesOwned" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 89
    "DonationContributor" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 90
    "MailOrderDonor" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 91
    "CharitableDonations" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 92
    "CharitableDonorAnimalWelfare" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 93
    "CharitableDonorArtsAndCulture" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 94
    "CharitableDonorChildren" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 95
    "CharitableDonorEnvironmentOrWildlife" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 96
    "CharitableDonorEnvironmentalIssues" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 97
    "CharitableDonorHealth" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 98
    "CharitableDonorInternationalAid" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 99
    "CharitableDonorPolitical" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 100
    "CharitableDonorConservativePolitics" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 101
    "CharitableDonorLiberalPolitics" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 102
    "CharitableDonorReligious" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 103
    "CharitableDonorVeterans" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 104
    "CharitableDonorUnspecified" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 105
    "CharitableDonorCommunity" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 106
    "VeteranInHousehold" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 107
    "Parenting" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 108
    "SingleParent" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 109
    "ApparelChildrenInfantsToddlers" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 110
    "ApparelChildrenLearningActivityToys" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 111
    "ApparelChildrenBabyCare" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 112
    "ApparelChildrenBackToSchool" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 113
    "ApparelChildrenGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 114
    "YoungAdultInHousehold" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 115
    "SeniorAdultInHousehold" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 116
    "ChildrenInterests" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 117
    "Grandchildren" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 118
    "ChristianFamilies" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 119
    "Pets" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 120
    "Equestrian" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 121
    "CatOwner" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 122
    "DogOwner" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 123
    "OtherPetOwner" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 124
    "CareerImprovement" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 125
    "WorkingWoman" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 126
    "AfricanAmericanProfessional" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 127
    "SOHOIndicator" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 128
    "Career" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 129
    "BooksMagazines" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 130
    "Books" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 131
    "AudioBooks" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 132
    "ReadingGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 133
    "ReadingReligiousOrInspirational" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 134
    "ReadingScienceFiction" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 135
    "ReadingMagazines" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 136
    "ReadingAudioBooks" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 137
    "ReadingGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 138
    "MilitaryHistory" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 139
    "CurrentAffairsAndPolitics" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 140
    "ReligiousInspirational" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 141
    "ScienceAndSpace" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 142
    "Magazines" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 143
    "OnlineEducation" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 144
    "Gaming" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 145
    "HomeComputingOfficeGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 146
    "DVDsVideos" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 147
    "TVVideoMovieWatcher" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 148
    "HomeComputingOffice" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 149
    "HighEndAppliances" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 150
    "IntendToPurchaseHDTVSatDish" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 151
    "MusicHomeStereo" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 152
    "MusicPlayer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 153
    "MusicCollector" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 154
    "MusicAvidListener" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 155
    "MovieCollector" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 156
    "TVCable" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 157
    "VideoGames" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 158
    "TVSatDish" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 159
    "Computers" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 160
    "ComputerGames" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 161
    "ConsumerElectronics" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 162
    "MusicGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 163
    "ElectronicsAndComputersGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 164
    "Telecommunications" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 165
    "Antiques" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 166
    "Art" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 167
    "TheaterPerformingArt" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 168
    "ArtsGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 169
    "MusicalInstruments" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 170
    "CollectiblesGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 171
    "CollectiblesStamps" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 172
    "CollectiblesCoins" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 173
    "CollectiblesArts" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 174
    "CollectiblesAntiques" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 175
    "CollectorAvid" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 176
    "CollectiblesAntiquesGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 177
    "CollectiblesSportsMemorabilia" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 178
    "CollectiblesMilitary" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 179
    "LifestyleInterestsCollectibles" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 180
    "DoItYourselferHomeImprovement" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 181
    "DoItYourselferAutoWork" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 182
    "DoItYourselferSewingKnitting" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 183
    "DoItYourselferWoodworking" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 184
    "DoItYourselferPhotography" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 185
    "DoItYourselferAviation" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 186
    "DoItYourselferHousePlants" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 187
    "DoItYourselferCrafts" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 188
    "DoItYourselferHomeAndGarden" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 189
    "DoItYourselferGardening" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 190
    "GeneralGardening" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 191
    "HomeImprovementGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 192
    "LifestylesCraftsHobbies" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 193
    "LifestylesPhotographyVideo" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 194
    "LifestylesSmoking" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 195
    "LifestylesDecoratingAndFurnishing" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 196
    "LifestylesHomeImprovement" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 197
    "LifestylesIntendToPurchaseHomeImprovement" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 198
    "LifestylesFoodAndWine" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 199
    "LifestylesCookingGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 200
    "LifestylesCookingGourmet" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 201
    "LifestylesNaturalFoods" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 202
    "LifestylesFoodGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 203
    "LifestylesBoardGamesAndPuzzles" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 204
    "LifestylesCasinoGaming" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 205
    "LifestylesSweepstakes" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 206
    "TravelGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 207
    "TravelGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 208
    "TravelDomestic" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 209
    "TravelInternational" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 210
    "TravelCruiseVacations" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 211
    "LivingHome" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 212
    "LivingDIY" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 213
    "LivingSporty" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 214
    "LivingUpscale" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 215
    "LivingCulturalArtistic" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 216
    "LivingHighbrow" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 217
    "LivingHighTech" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 218
    "LivingCommon" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 219
    "LivingProfessional" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 220
    "LivingBroader" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 221
    "ExerciseHealthGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 222
    "ExerciseRunningOrJogging" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 223
    "ExerciseWalking" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 224
    "ExerciseAerobic" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 225
    "SpectatorSportsAutoOrMotorcycleRacing" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 226
    "SpectatorSportsTVSports" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 227
    "SpectatorSportsFootball" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 228
    "SpectatorSportsBaseball" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 229
    "SpectatorSportsBasketball" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 230
    "SpectatorSportsHockey" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 231
    "SpectatorSportsSoccer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 232
    "SportsTennis" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 233
    "SportsGolf" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 234
    "SportsSnowSkiing" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 235
    "SportsMotorcycling" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 236
    "SportsNascar" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 237
    "SportsBoatingSailing" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 238
    "SportsScubaDiving" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 239
    "SportsSportsAndLeisure" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 240
    "SportsHunting" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 241
    "SportsFishing" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 242
    "SportsCampingOrHiking" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 243
    "SportsShooting" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 244
    "SportsGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 245
    "SportsOutdoorsGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 246
    "HealthMedical" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 247
    "DietingWeightLoss" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 248
    "SelfImprovement" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 249
    "AutoPartsAndAccessories" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 250 //start here, manual
    "HomeValue" => [
        'pattern' => array_keys($homeValues),
        'canBeEmpty' => true,
    ],
    # 251
    "HomePurchaseDate" => [
        'pattern' => '/^\d{8}$/',
        'canBeEmpty' => true,
    ],
    # 252
    "HomePurchasePrice" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => true,
    ],
    # 253
    "HomeSalesTransactionCode" => [
        'pattern' => array_keys($homeSalesTransactionCode),
        'canBeEmpty' => true,
    ],
    # 254
    "MortgageMostRecentAmount" => [
        'pattern' => null, // no values for this column?
        'canBeEmpty' => true,
    ],
    # 255
    "Mortgage2ndMostRecentAmount" => [
        'pattern' => '/^\d{4,7}$/',
        'canBeEmpty' => true,
    ],
    # 256
    "MortgagePurchaseAmount" => [
        'pattern' => '/^\d{2,4}$/',
        'canBeEmpty' => true,
    ],
    # 257
    "Mortgage2ndPurchaseAmount" => [
        'pattern' => '/^\d{4,6}$/',
        'canBeEmpty' => true,
    ],
    # 258
    "MortgageMostRecentDate" => [
        'pattern' => null, // no values for this column?
        'canBeEmpty' => true,
    ],
    # 259
    "Mortgage2ndMostRecentDate" => [
        'pattern' => '/^\d{8}$/',
        'canBeEmpty' => true,
    ],
    # 260
    "MortgagePurchaseDate" => [
        'pattern' => '/^\d{8}$/',
        'canBeEmpty' => true,
    ],
    # 261
    "MortgageMostRecentLoanTypeCode" => [
        'pattern' => array_keys($mortgageMostRecentLoanTypeCode),
        'canBeEmpty' => true,
    ],
    # 262
    "Mortgage2ndMostRecentLoanTypeCode" => [
        'pattern' => array_keys($mortgageMostRecentLoanTypeCode),
        'canBeEmpty' => true,
    ],
    # 263
    "MortgagePurchaseLoanTypeCode" => [
        'pattern' => array_keys($mortgageMostRecentLoanTypeCode),
        'canBeEmpty' => true,
    ],
    # 264
    "Mortgage2ndPurchaseLoanTypeCode" => [
        'pattern' => array_keys($mortgageMostRecentLoanTypeCode),
        'canBeEmpty' => true,
    ],
    # 265
    "MortgageMostRecentLenderCode" => [
        'pattern' => '/^\d{3}$/',
        'canBeEmpty' => true,
    ],
    # 266
    "Mortgage2ndMostRecentLenderCode" => [
        'pattern' => '/^\d{3}$/',
        'canBeEmpty' => true,
    ],
    # 267
    "MortgagePurchaseLenderCode" => [
        'pattern' => '/^\d{3}$/',
        'canBeEmpty' => true,
    ],
    # 268
    "MortgageMostRecentLenderName" => [
        'pattern' => '/^[A-Z0-9\s\-\'\/\.\&\(\)\*]+$/',
        'canBeEmpty' => true,
    ],
    # 269
    "Mortgage2ndMostRecentLenderName" => [
        'pattern' => '/^[A-Z0-9\s\-\'\/\.\&\(\)\*]+$/',
        'canBeEmpty' => true,
    ],
    # 270
    "MortgagePurchaseLenderName" => [
        'pattern' => '/^[A-Z0-9\s\-\'\/\.\&\(\)\*]+$/',
        'canBeEmpty' => true,
    ],
    # 271
    "MortgageMostRecentInterestRateType" => [
        'pattern' => null, // no values for this column?
        'canBeEmpty' => true,
    ],
    # 272
    "Mortgage2ndMostRecentInterestRateType" => [
        'pattern' => '/^[A-F]$/',
        'canBeEmpty' => true,
    ],
    # 273
    "MortgagePurchaseInterestRateType" => [
        'pattern' => null, // no values for this column?
        'canBeEmpty' => true,
    ],
    # 274
    "Mortgage2ndPurchaseInterestRateType" => [
        'pattern' => null, // no values for this column?
        'canBeEmpty' => true,
    ],
    # 275
    "MortgageMostRecentInterestRate" => [
        'pattern' => '/^\d{3,5}$/',
        'canBeEmpty' => true,
    ],
    # 276
    "Mortgage2ndMostRecentInterestRate" => [
        'pattern' => '/^\d{3,5}$/',
        'canBeEmpty' => true,
    ],
    # 277
    "MortgagePurchaseInterestRate" => [
        'pattern' => '/^\d{4}$/',
        'canBeEmpty' => true,
    ],
    # 278
    "Mortgage2ndPurchaseInterestRate" => [
        'pattern' => '/^\d{4}$/',
        'canBeEmpty' => true,
    ],
    # 279
    "HomeYearBuilt" => [
        'pattern' => '/^\d{4}$/',
        'canBeEmpty' => true,
    ],
    # 280
    "HomeAirConditioning" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 281
    "HomePool" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 282
    "HomeFuel" => [
        'pattern' => array_keys($homeFuel),
        'canBeEmpty' => true,
    ],
    # 283
    "HomeSewer" => [
        'pattern' => array_keys($homeSewer),
        'canBeEmpty' => true,
    ],
    # 284
    "HomeWater" => [
        'pattern' => array_keys($homeWater),
        'canBeEmpty' => true,
    ],
    # 285
    "LoanToValue" => [
        'pattern' => '/^\d{1,3}$/',
        'canBeEmpty' => true,
    ],
    # 286
    "EthnicCode" => [
        'pattern' => array_keys($ethnicCodes),
        'canBeEmpty' => true,
    ],
    # 287
    "EthnicConfidence" => [
        'pattern' => '/^[A-D]$/',
        'canBeEmpty' => true,
    ],
    # 288
    "EthnicGroup" => [
        'pattern' => array_keys($ethnicGroups),
        'canBeEmpty' => true,
    ],
    # 289
    "Language" => [
        'pattern' => array_keys($languageCodes),
        'canBeEmpty' => true,
    ],
    # 290
    "Religion" => [
        'pattern' => array_keys($religion),
        'canBeEmpty' => true,
    ],
    # 291
    "HispanicCountryCode" => [
        'pattern' => array_keys($hispanicCountryCodes),
        'canBeEmpty' => true,
    ],
    # 292
    "AssimilationCode" => [
        'pattern' => '/^[A-D]$/',
        'canBeEmpty' => true,
    ],
    # 293
    "CreditRating" => [
        'pattern' => array_keys($creditRating),
        'canBeEmpty' => true,
    ],
    # 294
    "DNCFlag" => [
        'pattern' => '/^[F,T]$/',
        'canBeEmpty' => true,
    ],
    # 295
    "PermIndividualID" => [
        'pattern' => '/^\d{1,9}$/',
        'canBeEmpty' => true,
    ],

    // TODO: (296-303) Not sure what these values are suppose to be
    # 296
    "AddressPrimary" => [
        'pattern' => null,
        'canBeEmpty' => true,
    ],
    # 297
    "AddressPre" => [
        'pattern' => null,
        'canBeEmpty' => true,
    ],
    # 298
    "AddressStreet" => [
        'pattern' => null,
        'canBeEmpty' => true,
    ],
    # 299
    "AddressPost" => [
        'pattern' => null,
        'canBeEmpty' => true,
    ],
    # 300
    "AddressSuffix" => [
        'pattern' => null,
        'canBeEmpty' => true,
    ],
    # 301
    "AddressAbrev" => [
        'pattern' => null,
        'canBeEmpty' => true,
    ],
    # 302
    "AddressSecy" => [
        'pattern' => null,
        'canBeEmpty' => true,
    ],
    # 303
    "PropertyType" => [
        'pattern' => null,
        'canBeEmpty' => true,
    ],
    # 304
    "Males0_2" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 305
    "Females0_2" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 306
    "UnkGender0_2" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 307
    "Males3_5" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 308
    "Females3_5" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 309
    "UnkGender3_5" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 310
    "Males6_10" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 311
    "Females6_10" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 312
    "UnkGender6_10" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 313
    "Males11_15" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 314
    "Females11_15" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 315
    "UnkGender11_15" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 316
    "Males16_17" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 317
    "Females16_17" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 318
    "UnkGender16_17" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 319
    "Males18_24" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 320
    "Females18_24" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 321
    "UnkGender18_24" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 322
    "Males25_34" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 323
    "Females25_34" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 324
    "UnkGender25_34" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 325
    "Males35_44" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 326
    "Females35_44" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 327
    "UnkGender35_44" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 328
    "Males45_54" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 329
    "Females45_54" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 330
    "UnkGender45_54" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 331
    "Males55_64" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 332
    "Females55_64" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 333
    "UnkGender55_64" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 334
    "Males65_74" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 335
    "Females65_74" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 336
    "UnkGender65_74" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 337
    "Males75" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 338
    "Females75" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 339
    "UnkGender75" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 340
    "People0_2" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 341
    "People3_5" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 342
    "People6_10" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 343
    "People11_15" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 344
    "People16_17" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 345
    "People18_24" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 346
    "People25_34" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 347
    "People35_44" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 348
    "People45_54" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 349
    "People55_64" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 350
    "People65_74" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 351
    "People75_and_up" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 352
    "DPC" => [
        'pattern' => '/^\d{3}$/',
        'canBeEmpty' => true,
    ],
    # 353
    "MSA" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => false,
    ],
    # 354
    "Country" => [
        'pattern' => '/^[A-Za-z]$/',
        'canBeEmpty' => false,
    ],
    # 355
    "CRRT" => [
        'pattern' => '/^[A-Z]\d{3}$/',
        'canBeEmpty' => true,
    ],
    # 356
    "CensusTract" => [
        'pattern' => '/^\d{6}$/',
        'canBeEmpty' => true,
    ],
    # 357
    "CensusBlock" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => true,
    ],
    # 358
    "Latitude" => [
        'pattern' => '/^\d{1,2}\.\d{6}$/i',
        'canBeEmpty' => false,
    ],
    # 359
    "Latitude" => [
        'pattern' => '/^\d{1,2}\.\d{6}$/i',
        'canBeEmpty' => false,
    ],
    # 360
    "ValueHunter" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 361
    "OpportunitySeekers" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 362
    "NewsAndFinancial" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 363
    "AutomotiveBuff" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 364
    "ComputerOwner" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 365
    "CookingEnthusiast" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 366
    "DoItYourselfer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 367
    "ExerciseEnthusiast" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 368
    "OutdoorEnthusiast" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 369
    "OutdoorSportsLover" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 370
    "Photography" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 371
    "Traveler" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 372
    "CatalogResponder" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 373
    "ReligiousMagazines" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 374
    "MaleMerchandiseBuyer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 375
    "FemaleMerchandiseBuyer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 376
    "GardeningFarmingBuyer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 377
    "BookBuyer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 378
    "SpecialFoodsBuyer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 379
    "TravelHeavyBusinessTraveler" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 380
    "HighTechLeader" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 381
    "DonatesToEnvironmentalCauses" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 382
    "DonatesByMail" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 383
    "IsCharitable" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 384
    "IsGeneralContributor" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 385
    "ChildrensProductsGeneralBackToSchool" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 386
    "ChildrensProductsGeneralBabyCare" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 387
    "ChildrensProductsGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 388
    "ResidentialDeliveryIndicator" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 389
    "CRAIncomeClassificationCode" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => true,
    ],
    # 390
    "IsMortgageMostRecentLenderNameAvailable" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 391
    "MortgageMostRecentAmountCode" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => true,
    ],
    # 392
    "HomeRefinanceDate" => [
        'pattern' => '/^\d{8}$/',
        'canBeEmpty' => true,
    ],
    # 393
    "RefinanceAmount" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => true,
    ],
    # 394
    "RefinanceAmountCode" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => true,
    ],
    # 395
    "RefinanceLenderName" => [
        'pattern' => '/^[A-Z0-9\s\-\'\/\.\&\(\)\*]+$/',
        'canBeEmpty' => true,
    ],
    # 396
    "IsRefinanceLenderNameAvailable" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 397
    "RefinanceRateType" => [
        'pattern' => array_keys($refinanceRateType),
        'canBeEmpty' => true,
    ],
    # 398
    "RefinanceLoanType" => [
        'pattern' => array_keys($refinanceLoanType),
        'canBeEmpty' => true,
    ],
    # 399
    "Age" => [
        'pattern' => '/^\d{1,3}$/',
        'canBeEmpty' => true,
    ],
    # 400
    "EmailAddr" => [
        'pattern' => '/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/',
        'canBeEmpty' => true,
    ],
    # 401
    "UserName" => [
        'pattern' => '/^[A-Za-z0-9._%+-]+$/',
        'canBeEmpty' => true,
    ],
    # 402
    "Domain" => [
        'pattern' => '/^[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/',
        'canBeEmpty' => true,
    ],
    # 403
    "PoliticalParty" => [
        'pattern' => '/^[A-Z]+$/',
        'canBeEmpty' => true,
    ],
    # 404
    "CongressionalDistrict" => [
        'pattern' => '/^[A-Z]+\:\d{2}$/',
        'canBeEmpty' => true,
    ],
    # 405
    "AutoYear" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => true,
    ],
    # 406
    "AutoMake" => [
        'pattern' => '/^[A-Z-]+$/',
        'canBeEmpty' => true,
    ],
    # 407
    "AutoModel" => [
        'pattern' => '/^[A-Z0-9-\s]+$/',
        'canBeEmpty' => true,
    ],
    # 408
    "AutoEdition" => [
        'pattern' => null, //no values for this column
        'canBeEmpty' => true,
    ],
    # 409
    "AutoTrim" => [
        'pattern' => null, //no values for this column
        'canBeEmpty' => true,
    ],
    # 410
    "VIN" => [
        'pattern' => '/^[A-Z0-9]{17}$/',
        'canBeEmpty' => true,
    ],
    # 411
    "VFFitness" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 412
    "VFHealthInsurance" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 413
    "VFLifeInsurance" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 414
    "VFTricare" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 415
    "VFNutra" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 416
    "VFDiabetic" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 417
    "VFDisability" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 418
    "OPISocialActivity" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
    ],
    # 419
    "OPIForums" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 420
    "OPIBlogs" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 421
    "OPIFileSharing" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 422
    "OPICommerce" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
    ],
    # 423
    "OPIAuction" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 424
    "OPIEntertainment" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
    ],
    # 425
    "OPICommunication" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 426
    "OPIRealEstate" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 427
    "OPIGames" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 428
    "OPIEducation" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 429
    "OPIApparel" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 430
    "OPIAuto" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 431
    "MobilePhone" => [
        'pattern' => '/^\d{8,10}$/',
        'canBeEmpty' => true,
    ],
    # 432
    "MobileLineType" => [
        'pattern' => '/^[BLVW]$/',
        'canBeEmpty' => true,
    ],
    # 433
    "MobileCarrier" => [
        'pattern' => '/^[A-Za-z0-9&-.\/\s]{3,}$/',
        'canBeEmpty' => true,
    ],
    # 434
    "DeviceIDPIPE" => [
        'pattern' => null, //no values for this column
        'canBeEmpty' => true,
    ],
    # 435
    "FA_cc" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
    ],
    # 436
    "FA_PDLCtr" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
    ],
    # 437
    "FA_NumLoans" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
    ],
    # 438
    "FA_AvgLoan" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => false,
    ],
    # 439
    "FA_AutoCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 440
    "FA_AutoInsCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 441
    "FA_LifeCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 442
    "FA_MortCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 443
    "FA_TaxCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 444
    "FA_Past3MosCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 445
    "FA_Past6MosCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 446
    "FA_Past12MosCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 447
    "FA_Past24MosCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 448
    "FA_Over24MosCtr" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
    ],
    # 449
    "CityFIPS" => [
        'pattern' => '/^\d{5}|$/',
        'canBeEmpty' => true,
    ],
    # 450
    "StateCountyFIPS" => [
        'pattern' => '/^\d{5}$/',
        'canBeEmpty' => true,
    ],
    # 451
    "CVGroupAddress" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 452
    "CVGroupPhone" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 453
    "CVGroupEmailAddr" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 454
    "CVGroupNamePhone" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 455
    "CVGroupNameEmailAddr" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 456
    "CVGroupNameAddress" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 457
    "CVGroupMobilePhone" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 458
    "CVGroupMobilePhoneName" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 459
    "PhoneLineType" => [
        'pattern' => '/^[BLVW]$/',
        'canBeEmpty' => true,
    ],
    # 460
    "PhoneCarrier" => [
        'pattern' => '/^[A-Za-z0-9&-.\/\s]{3,}$/',
        'canBeEmpty' => true,
    ],
    # 461
    "MobilePhoneLineType" => [
        'pattern' => '/^[BLVW]$/',
        'canBeEmpty' => true,
    ],
    # 465
    "PROP_AreaBuilding" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 466
    "PROP_AreaFirstFloor" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 483
    "AUDEX_DevIDPIPE" => [
        'pattern' => '/^[A-Z0-9-|]+$/',
        'canBeEmpty' => true,
    ],
    # 484
    "AUDEX_EmailAddrPIPE" => [
        'pattern' => '/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}|$/',
        'canBeEmpty' => true,
    ],
    # 485
    "AUDEX_PhonePIPE" => [
        'pattern' => '/^\d{8,10}|$/',
        'canBeEmpty' => true,
    ],
];
